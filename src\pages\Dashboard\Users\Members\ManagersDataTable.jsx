import React, { useEffect, useState, useRef } from "react";
import { Fa<PERSON>ift, FaSearch, FaPhone, FaEnvelope, FaBuilding, FaUser } from "react-icons/fa";
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { InputNumber } from "primereact/inputnumber";
import { But<PERSON> } from "primereact/button";
import { Toast } from "primereact/toast";
import axios from "axios";
import { Tooltip } from "primereact/tooltip";
import CreateCardToManagerForm from "../../Backages/CreateCardToManagerForm";

// Add these imports for the animated SVGs
import { motion } from "framer-motion";
import { HiOutlineCreditCard } from "react-icons/hi";
import { BsBank2 } from "react-icons/bs";

// Import the context hook
import { useDataTableContext } from "../../../../contexts/UsersDataTableContext";
import { useGlobalContext } from "../../../../contexts/GlobalContext";
import { useQueryParams } from "../../../../utils/helper";
import { useDeleteUserMutation } from "../../../../quires/user";
import { useLayout } from "../../../../contexts/LayoutContext";

import AssignGroupDialog from "../Groups/AssignGroupDialog";
import AddMemberDialog from "./AddMemberDialog";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";

// Define managers table config specifically for this component
const managersTableConfig = {
  url: "datatable/managers",
  filters: {
    'name': { value: '', matchMode: 'contains' },
    'email': { value: '', matchMode: 'contains' },
    'phone': { value: '', matchMode: 'contains' },
    'company_name': { value: '', matchMode: 'contains' },
    'status': { value: '', matchMode: 'contains' },
  }
};



function ManagersDataTable() {
  const {
    setLazyManagersParams,
    loading: tableLoading,
  } = useDataTableContext();
  const { dialogHandler, openDialog, setSelectedMembers } =
    useGlobalContext();
  const { isMobile } = useLayout();

  const queryParams = useQueryParams();
  const groupID = queryParams.get("group-id");
  const designID = queryParams.get("design-id");
  const deleteRow = useDeleteUserMutation();

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  const [selectedMember, setSelectedMember] = useState();
  const [actionType, setActionType] = useState("create"); // create or update

  // Manager detail modal state
  const [selectedManagerForModal, setSelectedManagerForModal] = useState(null);
  const [managerDetailModalVisible, setManagerDetailModalVisible] = useState(false);
  const [giftDialogVisible, setGiftDialogVisible] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [availablePackages, setAvailablePackages] = useState([]);
  const [giftLoading, setGiftLoading] = useState(false);  // تغيير اسم المتغير هنا
  const [formData, setFormData] = useState({
    user_id: null,
    package_id: null,
    duration: 12,
    payment_method: "gift",
    total_price: 0
  });
  const toastRef = useRef(null);  // تغيير من useState إلى useRef

  // Add state for animation
  const [animationKey, setAnimationKey] = useState(0);

  // 1. أضف متغير allManagers
  const [allManagers, setAllManagers] = useState([]);

  // استخدم بيانات محلية للجدول
  const [data, setData] = useState([]);

  useEffect(() => {
    const fetchAllManagers = async () => {
      try {
        // جلب كل المدراء بدون فلترة
        const token = localStorage.getItem("token");
        const response = await axios.get(
          `${import.meta.env.VITE_BACKEND_URL}/datatable/managers?page=1&per_page=10000&render_html=0`,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        setAllManagers(response.data.data || []);
        setLazyManagersParams(prev => ({ ...prev, totalRecords: response.data.pagination?.total || 0 }));
        setSelectedMembers({ data: [] });
        setData(response.data.data || []);
      } catch (error) {
        console.error("Error fetching managers:", error);
        toastRef.current?.show({
          severity: "error",
          summary: "Error",
          detail: "Failed to fetch managers",
          life: 3000
        });
      }
    };
    fetchAllManagers();
  }, [groupID, designID]);

  // 2. عدل useEffect لجلب البيانات مرة واحدة فقط وتخزينها في allManagers وdata
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (searchQuery) {
        setData(
          allManagers.filter((m) =>
            m.name?.toLowerCase().includes(searchQuery.toLowerCase())
          )
        );
      } else {
        setData(allManagers);
      }
    }, 300);
    return () => clearTimeout(timeout);
  }, [searchQuery, allManagers]);

  const createMember = () => {
    setActionType("create");
    setSelectedMember({});
    dialogHandler("addMember");
  };



  const editMember = (data) => {
    setActionType("update");
    const updatedData = { ...data };
    delete updatedData.role;
    delete updatedData.group_permission;
    setSelectedMember(updatedData);
    dialogHandler("addMember");
  };

  const deleteRowHandler = async (rowData) => {
    await deleteRow.mutateAsync(
      { id: rowData?.id },
      {
        onSuccess: () => {
          setLazyManagersParams((prev) => ({ ...prev, ...managersTableConfig }));
        },
      }
    );
  };

  const fetchAvailablePackages = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/packages/original_packages`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      setAvailablePackages(response.data || []);
    } catch (error) {
      console.error("Error fetching packages:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch available packages",
        life: 3000
      });
    }
  };

  // تقديم نموذج الهدية
  const handleGiftSubmit = async () => {
    try {
      setGiftLoading(true);
      const token = localStorage.getItem("token");

      // For bank_transfer, we'll use the manually entered total_price directly
      // No need to recalculate it here

      await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/admin/assign-package-to-user`,
        {
          user_id: formData.user_id,
          package_id: formData.package_id,
          duration: formData.duration,
          payment_method: formData.payment_method,
          total_price: formData.payment_method === "bank_transfer" ? formData.total_price : 0
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
          }
        }
      );

      toastRef.current.show({
        severity: "success",
        summary: "Success",
        detail: "Package has been successfully assigned to the user",
        life: 3000
      });

      setGiftDialogVisible(false);
      // Update data - use managersTableConfig instead of usersTableConfig
      setLazyManagersParams((prev) => ({ ...prev, ...managersTableConfig }));
    } catch (error) {
      console.error("Error assigning package:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: error.response?.data?.message || "An error occurred while assigning the package",
        life: 3000
      });
    } finally {
      setGiftLoading(false);
    }
  };

  const openGiftDialog = (rowData) => {
    setFormData({
      ...formData,
      user_id: rowData.id,
      package_id: null,
      duration: 12,
      payment_method: "gift"
    });
    fetchAvailablePackages();
    setGiftDialogVisible(true);
  };



  const Header = () => {
    const userRole = localStorage.getItem("user_role");
    return (
      <div className="w-full">
        <div className={`w-full mb-4 ${isMobile ? 'flex flex-col gap-3' : 'flex items-center'}`}>
          {/* Search Bar - Always centered */}
          <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[600px] mx-auto'}`}>
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search by manager name..."
              className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                        focus:outline-none focus:ring-2 focus:ring-blue-300
                        focus:border-blue-300 transition-all duration-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Add Manager button - Pushed to right on desktop */}
          <div className={`${isMobile ? 'w-full' : 'flex-shrink-0 ml-4'}`}>
            {userRole !== "user" && (
              <button
                className={`main-btn text-md shadow-md ${isMobile ? 'w-full' : ''}`}
                onClick={() => createMember()}
              >
                Add Manager
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Manager card click handler
  const handleManagerCardClick = (manager) => {
    setSelectedManagerForModal(manager);
    setManagerDetailModalVisible(true);
  };

  // Manager card component
  const ManagerCard = ({ manager }) => {
    const lastPackage = manager.packages && manager.packages.length > 0
      ? manager.packages[manager.packages.length - 1]
      : null;

    const getStatusColor = () => {
      if (!lastPackage) return "bg-gray-500";
      return lastPackage.status === "active" ? "bg-green-500" : "bg-red-500";
    };

    const getStatusText = () => {
      if (!lastPackage) return "No Package";
      return lastPackage.status;
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-xl shadow-lg hover:shadow-xl border border-gray-200 overflow-hidden cursor-pointer group"
        onClick={() => handleManagerCardClick(manager)}
      >
        {/* Card Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center space-x-4">
            {/* Profile Image */}
            <div className="relative">
              <img
                src={manager.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(manager.name)}&background=00c3ac&color=fff&size=64`}
                alt={manager.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 group-hover:border-[#00c3ac] transition-colors duration-300"
              />
              {/* <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${getStatusColor()}`}></div> */}
            </div>

            {/* Manager Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-bold text-gray-900 truncate group-hover:text-[#00c3ac] transition-colors duration-300">
                {manager.name}
              </h3>
              <p className="text-sm text-gray-600 truncate">
                <FaBuilding className="inline mr-1" />
                {manager.company_name || 'No Company'}
              </p>
              <div className="flex items-center mt-1">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white ${getStatusColor()}`}>
                  {getStatusText()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Card Body - Contact Info */}
        <div className="px-6 pb-4">
          <div className="space-y-2">
            {manager.email && (
              <div className="flex items-center text-sm text-gray-600">
                <FaEnvelope className="mr-2 text-gray-400" />
                <span className="truncate">{manager.email}</span>
              </div>
            )}
            {manager.phone && (
              <div className="flex items-center text-sm text-gray-600">
                <FaPhone className="mr-2 text-gray-400" />
                <span>{manager.phone}</span>
              </div>
            )}
            {manager.department && (
              <div className="flex items-center text-sm text-gray-600">
                <FaUser className="mr-2 text-gray-400" />
                <span>{manager.department}</span>
              </div>
            )}
          </div>
        </div>

        {/* Card Footer - Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">
              ID: {manager.id}
            </span>
            <div className="flex space-x-2">
              {/* Action buttons */}
              <Tooltip target={`.gift-btn-${manager.id}`} content="Gift Package" position="top" />
              <button
                className={`gift-btn-${manager.id} p-2 rounded-lg bg-purple-100 text-purple-600 hover:bg-purple-200 transition-colors duration-200`}
                onClick={(e) => {
                  e.stopPropagation();
                  openGiftDialog(manager);
                }}
              >
                <FaGift size={14} />
              </button>

              <Tooltip target={`.card-btn-${manager.id}`} content="Create Card" position="top" />
              <button
                className={`card-btn-${manager.id} p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors duration-200`}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedMember(manager);
                  dialogHandler("CreateCardToManagerForm", true);
                }}
              >
                <HiOutlineCreditCard size={14} />
              </button>

              <Tooltip target={`.edit-btn-${manager.id}`} content="Edit Manager" position="top" />
              <button
                className={`edit-btn-${manager.id} p-2 rounded-lg bg-yellow-100 text-yellow-600 hover:bg-yellow-200 transition-colors duration-200`}
                onClick={(e) => {
                  e.stopPropagation();
                  editMember(manager);
                }}
              >
                <FiEdit size={14} />
              </button>

              {manager.id.toString() !== localStorage.getItem("user_id") && (
                <>
                  <Tooltip target={`.delete-btn-${manager.id}`} content="Delete Manager" position="top" />
                  <button
                    className={`delete-btn-${manager.id} p-2 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 transition-colors duration-200`}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteRowHandler(manager);
                    }}
                  >
                    <TfiTrash size={14} />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <>
      <Toast ref={toastRef} />
      <div className="w-full mt-8">
        {/* Header */}
        <Header />

        {/* Cards Grid */}
        <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
          {data.map((manager) => (
            <ManagerCard key={manager.id} manager={manager} />
          ))}
        </div>

        {/* Empty State */}
        {data.length === 0 && !tableLoading && (
          <div className="text-center py-12">
            <FaUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No managers found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery ? 'Try adjusting your search terms.' : 'Get started by adding a new manager.'}
            </p>
          </div>
        )}

        {/* Loading State */}
        {tableLoading && (
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden animate-pulse">
                <div className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="px-6 pb-4">
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                    <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    <div className="flex space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Manager Detail Modal */}
        <Dialog
          visible={managerDetailModalVisible}
          style={{ width: isMobile ? "95vw" : "600px" }}
          header="Manager Details"
          modal
          className="p-fluid"
          onHide={() => setManagerDetailModalVisible(false)}
        >
          {selectedManagerForModal && (
            <div className="space-y-6">
              {/* Manager Header */}
              <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                <img
                  src={selectedManagerForModal.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedManagerForModal.name)}&background=00c3ac&color=fff&size=80`}
                  alt={selectedManagerForModal.name}
                  className="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg"
                />
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedManagerForModal.name}</h2>
                  <p className="text-lg text-gray-600">{selectedManagerForModal.company_name || 'No Company'}</p>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white mt-2 ${
                    selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0 && selectedManagerForModal.packages[selectedManagerForModal.packages.length - 1]?.status === "active"
                      ? "bg-green-500"
                      : selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0
                        ? "bg-red-500"
                        : "bg-gray-500"
                  }`}>
                    {selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0
                      ? selectedManagerForModal.packages[selectedManagerForModal.packages.length - 1]?.status
                      : "No Package"}
                  </span>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Contact Information</h3>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <FaEnvelope className="text-blue-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="text-gray-900">{selectedManagerForModal.email || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FaPhone className="text-green-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="text-gray-900">{selectedManagerForModal.phone || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FaBuilding className="text-purple-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Company</p>
                        <p className="text-gray-900">{selectedManagerForModal.company_name || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <FaUser className="text-orange-500 w-5 h-5" />
                      <div>
                        <p className="text-sm text-gray-500">Department</p>
                        <p className="text-gray-900">{selectedManagerForModal.department || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Additional Information</h3>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500">Manager ID</p>
                      <p className="text-gray-900 font-mono">{selectedManagerForModal.id}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">User Type</p>
                      <p className="text-gray-900 capitalize">{selectedManagerForModal.user_type || 'Manager'}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Position</p>
                      <p className="text-gray-900">{selectedManagerForModal.position || 'Not specified'}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Created At</p>
                      <p className="text-gray-900">{new Date(selectedManagerForModal.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Package Information */}
              {selectedManagerForModal.packages && selectedManagerForModal.packages.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Package Information</h3>
                  <div className="grid gap-3">
                    {selectedManagerForModal.packages.map((pkg, index) => (
                      <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">Package #{index + 1}</p>
                            <p className="text-sm text-gray-600">Status:
                              <span className={`ml-1 px-2 py-1 rounded text-xs font-medium text-white ${
                                pkg.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                              }`}>
                                {pkg.status}
                              </span>
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">Purchased</p>
                            <p className="text-sm text-gray-900">{new Date(pkg.purchased_at).toLocaleDateString()}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                <button
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200"
                  onClick={() => {
                    setManagerDetailModalVisible(false);
                    openGiftDialog(selectedManagerForModal);
                  }}
                >
                  <FaGift />
                  <span>Gift Package</span>
                </button>

                <button
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  onClick={() => {
                    setManagerDetailModalVisible(false);
                    setSelectedMember(selectedManagerForModal);
                    dialogHandler("CreateCardToManagerForm", true);
                  }}
                >
                  <HiOutlineCreditCard />
                  <span>Create Card</span>
                </button>

                <button
                  className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200"
                  onClick={() => {
                    setManagerDetailModalVisible(false);
                    editMember(selectedManagerForModal);
                  }}
                >
                  <FiEdit />
                  <span>Edit Manager</span>
                </button>

                {selectedManagerForModal.id.toString() !== localStorage.getItem("user_id") && (
                  <button
                    className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                    onClick={() => {
                      setManagerDetailModalVisible(false);
                      deleteRowHandler(selectedManagerForModal);
                    }}
                  >
                    <TfiTrash />
                    <span>Delete Manager</span>
                  </button>
                )}
              </div>
            </div>
          )}
        </Dialog>

        <Dialog
          visible={giftDialogVisible}
          style={{ width: "650px" }} // Increased from 500px to 650px
          header="Assign Package to User"
          modal
          className="p-fluid"
          footer={
            <div className="flex justify-between w-full pt-3"> {/* Changed from justify-end to justify-between */}
              <Button
                label="Cancel"
                icon="pi pi-times"
                onClick={() => setGiftDialogVisible(false)}
                className="p-button-danger p-button-rounded"
              />
              <Button
                label="Assign Package"
                icon="pi pi-gift"
                onClick={handleGiftSubmit}
                loading={giftLoading}
                className="p-button-success p-button-rounded"
              />
            </div>
          }
          onHide={() => setGiftDialogVisible(false)}
        >
          <div className="gift-form p-4">
            {/* Payment method icon animation */}
            <div className="flex justify-center mb-4">
              <motion.div
                key={animationKey}
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, type: "spring", bounce: 0.4 }}
                className="p-6 rounded-full bg-gray-100" // Increased padding
              >
                {formData.payment_method === "gift" ? (
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 10, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <FaGift size={80} className="text-purple-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                ) : (
                  <motion.div
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <BsBank2 size={80} className="text-blue-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                )}
              </motion.div>
            </div>

            <div className="field mb-4">
              <label htmlFor="package" className="block text-sm font-medium mb-2">
                Select Package
              </label>
              <Dropdown
                id="package"
                value={formData.package_id}
                options={availablePackages.map(pkg => ({
                  label: `${pkg.name} (${pkg.card_limit} cards)`,
                  value: pkg.id
                }))}
                onChange={(e) => {
                  const selectedPkg = availablePackages.find(p => p.id === e.value);
                  setFormData({
                    ...formData,
                    package_id: e.value,
                    total_price: formData.payment_method === "bank_transfer" ?
                      (formData.duration === 12 ? selectedPkg?.yearly_price : selectedPkg?.monthly_price) : 0
                  });
                  setSelectedPackage(selectedPkg);
                }}
                placeholder="Select a package"
                className="w-full"
                disabled={giftLoading}
                required
                optionLabel="label"
              />
              {selectedPackage && (
                <div className="mt-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Monthly Price:</span>
                    <span>${selectedPackage.monthly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Price:</span>
                    <span>${selectedPackage.yearly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Card Types:</span>
                    <span>{selectedPackage.card_type_names.join(', ')}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="field mb-4">
              <label htmlFor="duration" className="block text-sm font-medium mb-2">
                Subscription Duration
              </label>
              <Dropdown
                id="duration"
                value={formData.duration}
                options={[
                  { label: "Monthly (1 month)", value: 1 },
                  { label: "Quarterly (3 months)", value: 3 },
                  { label: "Semi-Annual (6 months)", value: 6 },
                  { label: "Annual (12 months)", value: 12 }
                ]}
                onChange={(e) => {
                  // Update duration
                  setFormData(prev => ({ ...prev, duration: e.value }));

                  // Suggest a price if package is selected and payment method is bank transfer
                  if (selectedPackage && formData.payment_method === "bank_transfer") {
                    let suggestedPrice;

                    if (e.value === 12) {
                      suggestedPrice = selectedPackage.yearly_price;
                    } else if (e.value === 6) {
                      suggestedPrice = (selectedPackage.monthly_price * 6) * 0.9; // 10% discount
                    } else if (e.value === 3) {
                      suggestedPrice = (selectedPackage.monthly_price * 3) * 0.95; // 5% discount
                    } else {
                      suggestedPrice = selectedPackage.monthly_price;
                    }

                    // Suggest the price but don't force it - user can still edit manually
                    setFormData(prev => ({
                      ...prev,
                      duration: e.value,
                      total_price: suggestedPrice
                    }));
                  }
                }}
                placeholder="Select duration"
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            <div className="field mb-4">
              <label htmlFor="payment_method" className="block text-sm font-medium mb-2">
                Payment Method
              </label>
              <Dropdown
                id="payment_method"
                value={formData.payment_method}
                options={[
                  { label: "Gift (Free)", value: "gift" },
                  { label: "Bank Transfer", value: "bank_transfer" }
                ]}
                onChange={(e) => {
                  setFormData({ ...formData, payment_method: e.value });
                  // Trigger animation when payment method changes
                  setAnimationKey(prev => prev + 1);
                }}
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            {formData.payment_method === "bank_transfer" && (
              <div className="field mb-4">
                <label htmlFor="total_price" className="block text-sm font-medium mb-2">
                  Total Price
                </label>
                <InputNumber
                  id="total_price"
                  value={formData.total_price}
                  onValueChange={(e) => setFormData({ ...formData, total_price: e.value })}
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  className="w-full"
                  disabled={giftLoading}
                  required
                />
                <small className="text-blue-600 mt-1 block">
                  You can adjust this price manually if needed.
                </small>
              </div>
            )}

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <i className="pi pi-info-circle mr-2"></i>
                {formData.payment_method === "gift"
                  ? "This will provide a free package to the user."
                  : "This will assign a paid package via bank transfer."}
              </p>
            </div>
          </div>
        </Dialog>

        {openDialog?.addMember && (
          <AddMemberDialog data={selectedMember} actionType={actionType} />
        )}
        {openDialog?.createGroup && (
          <AssignGroupDialog data={[]} />
        )}
        {openDialog?.updateGroup && <AssignGroupDialog />}

        {openDialog?.CreateCardToManagerForm &&
          selectedMember &&
          selectedMember.packages && (
            <CreateCardToManagerForm
              userId={selectedMember.id}
              packages={selectedMember.packages.filter(
                (pkg) => pkg.status === "active"
              )}
            />
          )}
      </div>
    </>
  );
}

export default ManagersDataTable;
